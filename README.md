# Gedsys .::Gateway::.

## Filtros de Autenticación

El gateway implementa varios filtros de autenticación para proteger las rutas y verificar la identidad de los usuarios.

### AuthenticationFilter

Este filtro se aplica a varias rutas y utiliza el `ExternalUserGatewayFilterImpl` para realizar la autenticación. Verifica la identidad del usuario mediante dos mecanismos:

1. **Autenticación por token JWT**: Verifica el token JWT en el header `Authorization` haciendo una llamada al servicio de validación de tokens.
2. **Autenticación de usuario externo**: Utiliza el header `X-External-User-Id` para autenticar usuarios externos.

### ExternalUserGatewayFilterImpl

Este filtro implementa la lógica de autenticación para usuarios externos:

1. Si el header `X-External-User-Id` está presente:
   - Consulta los datos del usuario externo en el componente core usando el ID proporcionado
   - Verifica que el valor del header `X-External-User-Id` coincida con el `identificationNumber` del usuario externo
   - Si coincide, permite el acceso y agrega headers con la información del usuario
   - Si no coincide, rechaza la solicitud con un error 403 (Forbidden)

2. Si el header `X-External-User-Id` no está presente:
   - Utiliza el mecanismo estándar de autenticación por token JWT
   - Verifica el token en el header `Authorization`
   - Si el token es válido, permite el acceso y agrega headers con la información del usuario
   - Si el token no es válido o no está presente, rechaza la solicitud

## Configuración de Rutas

El filtro `AuthenticationFilter` se aplica a las siguientes rutas:

- `base-route` (/core/**)
- `engine-custom-route` (/engine/api/**)
- `files-main-route` (/files/**)
- `aware-main-route` (/aware/api/**)
- `artificial-intelligence-component` (/ai/**)

## Configuración del CORS (CROSS-ORIGIN RESOURCE SHARING)

Modificar las siguientes propiedades en el archivo de propiedades según el perfil activo y las necesidades:

```yaml
cors:
  allowed-origins: "http:localhost:4200"
  allowed-methods: "*"
  allowed-headers: "Origin, Content-Type, Accept, Authorization"
  mapping: "/**"
```

Estos valores son los predeterminados, inspeccionar el archivo `src/resources/META-INF/additional-spring-configuration-metadata.json` para ver la definición de las propiedades.
El filtro que implementa el cors es `src/main/java/co/com/alpha/gateway/config/CorsConfig.java`.

## Sistema de Logging Estructurado

El gateway implementa un sistema de logging estructurado que proporciona trazabilidad completa y información contextual para debugging y monitoreo.

### Características del Logging

- **Logging estructurado** con información contextual (Request ID, IP cliente, User-Agent)
- **Sanitización automática** de información sensible (tokens, passwords)
- **Timing de operaciones** para análisis de performance
- **Niveles de log apropiados** (ERROR, WARN, INFO, DEBUG)
- **Trazabilidad completa** de requests a través de todos los filtros

### Información Registrada

#### Por Request:
- **Request ID**: Identificador único generado automáticamente
- **IP Cliente**: Extraído de headers X-Forwarded-For, X-Real-IP o dirección remota
- **User-Agent**: Información del cliente
- **Path y método HTTP**: Ruta y verbo HTTP del request
- **Timing**: Duración de operaciones críticas

#### Por Filtro:
- **Entrada y salida** con timing
- **Operaciones de autenticación** (JWT, usuarios externos)
- **Transformaciones de path** y headers
- **Errores detallados** con contexto completo
- **Llamadas WebClient** con URLs y respuestas

### Configuración de Logging

```yaml
logging:
  level:
    co.com.gedsys.gateway: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId:-}] [%X{clientIp:-}] %logger{36} - %msg%n"
```

### Ejemplos de Logs

#### Autenticación Exitosa:
```
2025-01-01 10:00:00.123 [reactor-http-nio-2] INFO [req-abc123] ExternalUserGatewayFilter - Usuario externo autenticado: 12345 - Estado: ACTIVO en 45ms
```

#### Error de Autenticación:
```
2025-01-01 10:00:00.456 [reactor-http-nio-3] WARN [req-def456] ExternalUserGatewayFilter - Usuario externo no encontrado: 99999 - Request: req-def456
```

#### Transformación de Path:
```
2025-01-01 10:00:00.789 [reactor-http-nio-4] INFO [req-ghi789] SemanticSearchGatewayFilter - Path reescrito: /semantic-search/test -> /webhook/documents/semantic-search/test
```

### Troubleshooting

#### Buscar por Request ID:
```bash
grep "req-abc123" application.log
```

#### Filtrar errores de autenticación:
```bash
grep "autenticación.*ERROR" application.log
```

#### Analizar performance:
```bash
grep "completado en.*ms" application.log | sort -k8 -n
```

### Seguridad

- **Tokens sanitizados**: Los tokens JWT y secrets se enmascaran automáticamente en logs
- **Headers sensibles**: Authorization headers se muestran como "Bearer ***MASKED***"
- **Información PII**: Datos personales se truncan o enmascaran según sea necesario
