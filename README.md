# Gedsys .::Gateway::.

## Filtros de Autenticación

El gateway implementa varios filtros de autenticación para proteger las rutas y verificar la identidad de los usuarios.

### AuthenticationFilter

Este filtro se aplica a varias rutas y utiliza el `ExternalUserGatewayFilterImpl` para realizar la autenticación. Verifica la identidad del usuario mediante dos mecanismos:

1. **Autenticación por token JWT**: Verifica el token JWT en el header `Authorization` haciendo una llamada al servicio de validación de tokens.
2. **Autenticación de usuario externo**: Utiliza el header `X-External-User-Id` para autenticar usuarios externos.

### ExternalUserGatewayFilterImpl

Este filtro implementa la lógica de autenticación para usuarios externos:

1. Si el header `X-External-User-Id` está presente:
   - Consulta los datos del usuario externo en el componente core usando el ID proporcionado
   - Verifica que el valor del header `X-External-User-Id` coincida con el `identificationNumber` del usuario externo
   - Si coincide, permite el acceso y agrega headers con la información del usuario
   - Si no coincide, rechaza la solicitud con un error 403 (Forbidden)

2. Si el header `X-External-User-Id` no está presente:
   - Utiliza el mecanismo estándar de autenticación por token JWT
   - Verifica el token en el header `Authorization`
   - Si el token es válido, permite el acceso y agrega headers con la información del usuario
   - Si el token no es válido o no está presente, rechaza la solicitud

## Configuración de Rutas

El filtro `AuthenticationFilter` se aplica a las siguientes rutas:

- `base-route` (/core/**)
- `engine-custom-route` (/engine/api/**)
- `files-main-route` (/files/**)
- `aware-main-route` (/aware/api/**)
- `artificial-intelligence-component` (/ai/**)

## Configuración del CORS (CROSS-ORIGIN RESOURCE SHARING)

Modificar las siguientes propiedades en el archivo de propiedades según el perfil activo y las necesidades:

```yaml
cors:
  allowed-origins: "http:localhost:4200"
  allowed-methods: "*"
  allowed-headers: "Origin, Content-Type, Accept, Authorization"
  mapping: "/**"
```

Estos valores son los predeterminados, inspeccionar el archivo `src/resources/META-INF/additional-spring-configuration-metadata.json` para ver la definición de las propiedades.
El filtro que implementa el cors es `src/main/java/co/com/alpha/gateway/config/CorsConfig.java`.
