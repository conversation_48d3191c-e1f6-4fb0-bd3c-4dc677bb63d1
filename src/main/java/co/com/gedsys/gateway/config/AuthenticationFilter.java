package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.filters.GatewayFilterImpl;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

@Component
public class AuthenticationFilter extends AbstractGatewayFilterFactory<AuthenticationFilter.Config> {

    private final GatewayFilterImpl gatewayFilter;

    public AuthenticationFilter(GatewayFilterImpl gatewayFilter) {
        super(Config.class);
        this.gatewayFilter = gatewayFilter;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return gatewayFilter;
    }

    public static class Config {
        // You can add configuration properties here if needed
    }
}
