package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.filters.ExternalUserGatewayFilterImpl;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

@Component
public class ExternalUserAuthenticationFilter extends AbstractGatewayFilterFactory<ExternalUserAuthenticationFilter.Config> {

    private final ExternalUserGatewayFilterImpl externalUserGatewayFilter;

    public ExternalUserAuthenticationFilter(ExternalUserGatewayFilterImpl externalUserGatewayFilter) {
        super(Config.class);
        this.externalUserGatewayFilter = externalUserGatewayFilter;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return externalUserGatewayFilter;
    }

    public static class Config {
        // You can add configuration properties here if needed
    }
}