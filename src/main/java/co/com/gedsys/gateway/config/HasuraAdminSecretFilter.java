package co.com.gedsys.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.springframework.http.HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN;

@Component
public class HasuraAdminSecretFilter implements GatewayFilter {

    private static final Logger log = Logger.getLogger(HasuraAdminSecretFilter.class.getName());
    private static final String HASURA_ADMIN_SECRET_HEADER = "x-hasura-admin-secret";

    private final String adminSecret;

    public HasuraAdminSecretFilter(@Value("${app.hasura.admin-secret}") String adminSecret) {
        this.adminSecret = adminSecret;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        if (shouldAddAdminSecret(request)) {
            if (log.isLoggable(Level.FINE)) {
                log.fine("Añadiendo header x-hasura-admin-secret a la solicitud");
            }
            
            return chain.filter(exchange.mutate()
                    .request(builder -> builder.headers(headers -> 
                        headers.set(HASURA_ADMIN_SECRET_HEADER, adminSecret)))
                    .build())
                .doFinally(signalType -> handleResponseHeaders(exchange));
        }
        
        return chain.filter(exchange)
            .doFinally(signalType -> handleResponseHeaders(exchange));
    }
    
    private boolean shouldAddAdminSecret(ServerHttpRequest request) {
        return request.getHeaders().get(HASURA_ADMIN_SECRET_HEADER) == null;
    }
    
    private void handleResponseHeaders(ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getResponse().getHeaders();
        List<String> originHeaders = headers.get(ACCESS_CONTROL_ALLOW_ORIGIN);
        
        if (originHeaders != null && originHeaders.size() > 1) {
            String firstOrigin = originHeaders.getFirst();
            
            if (log.isLoggable(Level.WARNING)) {
                log.log(Level.WARNING, "Múltiples headers {0} detectados: {1}", 
                    new Object[]{ACCESS_CONTROL_ALLOW_ORIGIN, originHeaders});
            }
            
            headers.set(ACCESS_CONTROL_ALLOW_ORIGIN, firstOrigin);
            
            if (log.isLoggable(Level.FINE)) {
                log.log(Level.FINE, "Headers {0} normalizados a: {1}", 
                    new Object[]{ACCESS_CONTROL_ALLOW_ORIGIN, firstOrigin});
            }
        }
    }
}
