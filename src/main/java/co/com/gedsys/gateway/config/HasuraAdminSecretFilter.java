package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.utils.LoggingUtils;
import co.com.gedsys.gateway.utils.RequestContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;

import static org.springframework.http.HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN;

@Component
public class HasuraAdminSecretFilter implements GatewayFilter {

    private static final Logger log = LoggerFactory.getLogger(HasuraAdminSecretFilter.class);
    private static final String HASURA_ADMIN_SECRET_HEADER = "x-hasura-admin-secret";
    private static final String FILTER_NAME = "HasuraAdminSecretFilter";

    private final String adminSecret;

    public HasuraAdminSecretFilter(@Value("${app.hasura.admin-secret}") String adminSecret) {
        this.adminSecret = adminSecret;
        if (adminSecret == null || adminSecret.trim().isEmpty()) {
            log.error("Hasura admin secret no configurado correctamente");
        } else {
            log.info("HasuraAdminSecretFilter inicializado correctamente");
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        long startTime = System.currentTimeMillis();
        LoggingUtils.logFilterEntry(log, FILTER_NAME, exchange);

        ServerHttpRequest request = exchange.getRequest();

        try {
            if (shouldAddAdminSecret(request)) {
                LoggingUtils.logHeaderOperation(log, "added", HASURA_ADMIN_SECRET_HEADER, exchange);

                return chain.filter(exchange.mutate()
                        .request(builder -> builder.headers(headers ->
                            headers.set(HASURA_ADMIN_SECRET_HEADER, adminSecret)))
                        .build())
                    .doFinally(signalType -> {
                        handleResponseHeaders(exchange);
                        LoggingUtils.logFilterExit(log, FILTER_NAME, exchange, startTime);
                    })
                    .onErrorResume(throwable -> {
                        LoggingUtils.logFilterError(log, FILTER_NAME, exchange,
                                "Error agregando header Hasura admin secret", throwable);
                        return Mono.error(throwable);
                    });
            }

            log.debug("Header {} ya presente, no se agrega - Request: {}",
                    HASURA_ADMIN_SECRET_HEADER, RequestContextUtils.getRequestId(exchange));

            return chain.filter(exchange)
                .doFinally(signalType -> {
                    handleResponseHeaders(exchange);
                    LoggingUtils.logFilterExit(log, FILTER_NAME, exchange, startTime);
                });

        } catch (Exception e) {
            LoggingUtils.logFilterError(log, FILTER_NAME, exchange,
                    "Error en filtro Hasura admin secret", e);
            return Mono.error(e);
        }
    }
    
    private boolean shouldAddAdminSecret(ServerHttpRequest request) {
        boolean shouldAdd = request.getHeaders().get(HASURA_ADMIN_SECRET_HEADER) == null;
        if (!shouldAdd) {
            log.debug("Header {} ya presente en request", HASURA_ADMIN_SECRET_HEADER);
        }
        return shouldAdd;
    }

    private void handleResponseHeaders(ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getResponse().getHeaders();
        List<String> originHeaders = headers.get(ACCESS_CONTROL_ALLOW_ORIGIN);

        if (originHeaders != null && originHeaders.size() > 1) {
            String firstOrigin = originHeaders.getFirst();

            LoggingUtils.logCorsMultipleOrigins(log, originHeaders.toString(), exchange);
            headers.set(ACCESS_CONTROL_ALLOW_ORIGIN, firstOrigin);
            LoggingUtils.logCorsNormalized(log, firstOrigin, exchange);
        }
    }
}
