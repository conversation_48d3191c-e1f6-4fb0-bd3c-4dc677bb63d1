package co.com.gedsys.gateway.config;

import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

@Component
public class HasuraAdminSecretGatewayFilterFactory extends AbstractGatewayFilterFactory<HasuraAdminSecretGatewayFilterFactory.Config> {

    private final HasuraAdminSecretFilter hasuraAdminSecretFilter;

    public HasuraAdminSecretGatewayFilterFactory(HasuraAdminSecretFilter hasuraAdminSecretFilter) {
        super(Config.class);
        this.hasuraAdminSecretFilter = hasuraAdminSecretFilter;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return hasuraAdminSecretFilter;
    }

    public static class Config {
        // Configuración vacía, no se requieren propiedades adicionales
    }
}
