package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.filters.SemanticSearchGatewayFilterImpl;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

@Component
public class SemanticSearchAuthenticationFilter extends AbstractGatewayFilterFactory<SemanticSearchAuthenticationFilter.Config> {

    private final SemanticSearchGatewayFilterImpl semanticSearchGatewayFilter;

    public SemanticSearchAuthenticationFilter(SemanticSearchGatewayFilterImpl semanticSearchGatewayFilter) {
        super(Config.class);
        this.semanticSearchGatewayFilter = semanticSearchGatewayFilter;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return semanticSearchGatewayFilter;
    }

    public static class Config {
        // You can add configuration properties here if needed
    }
} 