package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.utils.LoggingConstants;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebClientConfig {

    private static final Logger log = LoggerFactory.getLogger(WebClientConfig.class);

    @Value("${app.webclient.gedsys2-base.uri}")
    private String coreBaseUrl;

    @Value("${app.webclient.connect-timeout:5000}")
    private int connectTimeout;

    @Value("${app.webclient.read-timeout:30}")
    private int readTimeout;

    @Value("${app.webclient.write-timeout:30}")
    private int writeTimeout;

    @Bean
    WebClient coreWebClient() {
        log.info(LoggingConstants.CONFIG_LOADED, "coreWebClient.baseUrl", coreBaseUrl);
        log.info(LoggingConstants.CONFIG_LOADED, "coreWebClient.connectTimeout", connectTimeout + "ms");
        log.info(LoggingConstants.CONFIG_LOADED, "coreWebClient.readTimeout", readTimeout + "s");
        log.info(LoggingConstants.CONFIG_LOADED, "coreWebClient.writeTimeout", writeTimeout + "s");

        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout)
                .responseTimeout(Duration.ofSeconds(readTimeout))
                .doOnConnected(conn ->
                    conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.SECONDS)))
                .doOnRequest((request, connection) ->
                    log.debug("WebClient conectando a: {}", request.uri()))
                .doOnResponse((response, connection) ->
                    log.debug("WebClient respuesta recibida: {} desde {}",
                            response.status(), response.uri()));

        return WebClient.builder()
                .baseUrl(coreBaseUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .filter(logRequest())
                .filter(logResponse())
                .filter(handleErrors())
                .build();
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            log.debug("WebClient Request: {} {} - Headers: {}",
                    clientRequest.method(),
                    clientRequest.url(),
                    clientRequest.headers().keySet());
            return Mono.just(clientRequest);
        });
    }

    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            log.debug("WebClient Response: {} - Headers: {}",
                    clientResponse.statusCode(),
                    clientResponse.headers().asHttpHeaders().keySet());
            return Mono.just(clientResponse);
        });
    }

    private ExchangeFilterFunction handleErrors() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (clientResponse.statusCode().isError()) {
                log.warn("WebClient error response: {} - URL: {}",
                        clientResponse.statusCode().value(),
                        clientResponse.request().getURI());
            }
            return Mono.just(clientResponse);
        });
    }
}