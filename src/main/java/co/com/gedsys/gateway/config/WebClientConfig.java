package co.com.gedsys.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {

    @Value("${app.webclient.gedsys2-base.uri}")
    private String coreBaseUrl;

    @Bean
    WebClient coreWebClient() {
        return WebClient.builder()
                .baseUrl(coreBaseUrl)
                .build();
    }
}