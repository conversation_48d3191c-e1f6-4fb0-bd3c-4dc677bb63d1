package co.com.gedsys.gateway.datastructures.client.responses.usuario_externo;

import java.util.List;

public record UsuarioExternoDataStructure(
                List<Propiedades> properties,
                String identificationNumber,
                String identificationType,
                String name,
                String notes,
                String salutation,
                String status,
                String id) {
        public record Propiedades(
                        String notes,
                        String propertyName,
                        String propertyType,
                        String propertyValue,
                        String id) {
        }

        public enum EstadoUsuarioExterno {
                ACTIVO,
                INACTIVO
        }
}
