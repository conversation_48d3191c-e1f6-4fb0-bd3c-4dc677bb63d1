package co.com.gedsys.gateway.filters;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gateway.datastructures.client.responses.usuario_externo.UsuarioExternoDataStructure;
import co.com.gedsys.gateway.dtos.TokenDetail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Component
public class ExternalUserGatewayFilterImpl implements GatewayFilter {
    Logger log = Logger.getLogger(ExternalUserGatewayFilterImpl.class.getName());
    private final WebClient.Builder webClient;
    private final String validatorUrl;
    private final WebClient coreWebClient;
    private final String externalUserEndpoint;
    private final ObjectMapper objectMapper;

    private static final String EXTERNAL_USER_ID_HEADER = "X-External-User-Id";

    public ExternalUserGatewayFilterImpl(
            WebClient.Builder webClient,
            @Value("${app.authentication-filter.token-validation-url}") String validatorUrl,
            WebClient coreWebClient,
            @Value("${app.webclient.gedsys2-base.endpoints[0].path}") String externalUserEndpoint,
            ObjectMapper objectMapper) {
        this.webClient = webClient;
        this.validatorUrl = validatorUrl;
        this.coreWebClient = coreWebClient;
        this.externalUserEndpoint = externalUserEndpoint;
        this.objectMapper = objectMapper;
    }

    public Mono<Void> onError(ServerWebExchange exchange, HttpStatus status, String message) {
        log.severe(message);
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        errorResponse.put("path", exchange.getRequest().getPath().value());

        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            return response.writeWith(Mono.just(response.bufferFactory().wrap(jsonResponse.getBytes())));
        } catch (JsonProcessingException e) {
            return Mono.error(e);
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.info("Validating external user or JWT token");
        var externalUserId = exchange.getRequest().getHeaders().getFirst(EXTERNAL_USER_ID_HEADER);
        var authorizationHeader = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION);
        var sourceHeader = exchange.getRequest().getHeaders().getFirst(CustomHeaders.SOURCE);

        if (externalUserId != null && !externalUserId.isEmpty()) {
            return authenticateExternalUser(exchange, chain, externalUserId);
        }

        if (authorizationHeader == null) {
            return onError(exchange, HttpStatus.UNAUTHORIZED, "Missing authorization header");
        }

        log.info("No external user ID provided, using standard JWT authentication");
        return webClient.build().get()
                .uri(validatorUrl)
                .header(AUTHORIZATION, authorizationHeader)
                .header(CustomHeaders.SOURCE, sourceHeader)
                .retrieve()
                .bodyToMono(TokenDetail.class)
                .flatMap(tokenDetail -> {
                    log.info("User authenticated: " + tokenDetail);
                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, tokenDetail.claims().preferredUsername())
                            .header(CustomHeaders.NAME, tokenDetail.claims().name())
                            .header(CustomHeaders.EMAIL, tokenDetail.claims().email())
                            .build();
                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> 
                    onError(exchange, HttpStatus.UNAUTHORIZED, "Token authentication failed: " + e.getMessage()))
                .onErrorResume(e -> 
                    onError(exchange, HttpStatus.UNAUTHORIZED, "An unexpected error occurred during authentication"));
    }

    private Mono<Void> authenticateExternalUser(ServerWebExchange exchange, GatewayFilterChain chain, String externalUserId) {
        log.info("Authenticating external user with ID: " + externalUserId);

        return coreWebClient.get()
                .uri(uriBuilder -> uriBuilder.path(externalUserEndpoint.replace("{id}", externalUserId)).build())
                .retrieve()
                .bodyToMono(UsuarioExternoDataStructure.class)
                .flatMap(userData -> {
                    log.info("External user data retrieved: " + userData);

                    if (!UsuarioExternoDataStructure.EstadoUsuarioExterno.ACTIVO.name().equals(userData.status())) {
                        log.warning("External user is not active. Status: " + userData.status());
                        return onError(exchange, HttpStatus.UNAUTHORIZED, "External user is not active");
                    }

                    log.info("External user authenticated successfully");
                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, userData.identificationNumber())
                            .header(CustomHeaders.NAME, userData.name())
                            .build();

                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    HttpStatus status = HttpStatus.valueOf(e.getStatusCode().value());
                    String message = "External user authentication failed: " + e.getMessage();
                    log.warning(message);
                    
                    if (e.getStatusCode().value() == 404) {
                        return onError(exchange, HttpStatus.UNAUTHORIZED, "External user not found");
                    }
                    
                    return onError(exchange, status, message);
                })
                .onErrorResume(e -> {
                    log.severe("Unexpected error during external user authentication: " + e.getMessage());
                    return onError(exchange, HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred during external user authentication");
                });
    }
}
