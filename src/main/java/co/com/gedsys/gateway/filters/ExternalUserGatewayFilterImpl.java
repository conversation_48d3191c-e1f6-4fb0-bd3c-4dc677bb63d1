package co.com.gedsys.gateway.filters;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gateway.datastructures.client.responses.usuario_externo.UsuarioExternoDataStructure;
import co.com.gedsys.gateway.dtos.TokenDetail;
import co.com.gedsys.gateway.utils.LoggingUtils;
import co.com.gedsys.gateway.utils.RequestContextUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Component
public class ExternalUserGatewayFilterImpl implements GatewayFilter {
    private static final Logger log = LoggerFactory.getLogger(ExternalUserGatewayFilterImpl.class);
    private final WebClient.Builder webClient;
    private final String validatorUrl;
    private final WebClient coreWebClient;
    private final String externalUserEndpoint;
    private final ObjectMapper objectMapper;

    private static final String EXTERNAL_USER_ID_HEADER = "X-External-User-Id";
    private static final String FILTER_NAME = "ExternalUserGatewayFilter";

    public ExternalUserGatewayFilterImpl(
            WebClient.Builder webClient,
            @Value("${app.authentication-filter.token-validation-url}") String validatorUrl,
            WebClient coreWebClient,
            @Value("${app.webclient.gedsys2-base.endpoints[0].path}") String externalUserEndpoint,
            ObjectMapper objectMapper) {
        this.webClient = webClient;
        this.validatorUrl = validatorUrl;
        this.coreWebClient = coreWebClient;
        this.externalUserEndpoint = externalUserEndpoint;
        this.objectMapper = objectMapper;
    }

    public Mono<Void> onError(ServerWebExchange exchange, HttpStatus status, String message) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String clientIp = RequestContextUtils.getClientIp(exchange);
        String path = RequestContextUtils.getPath(exchange);

        log.error("Error en filtro de autenticación - Status: {} - Message: {} - Request: {} - IP: {} - Path: {}",
                status.value(), message, requestId, clientIp, path);

        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        errorResponse.put("path", path);
        errorResponse.put("requestId", requestId);

        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            return response.writeWith(Mono.just(response.bufferFactory().wrap(jsonResponse.getBytes())));
        } catch (JsonProcessingException e) {
            log.error("Error serializando respuesta de error - Request: {} - Error: {}", requestId, e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        long startTime = System.currentTimeMillis();
        LoggingUtils.logFilterEntry(log, FILTER_NAME, exchange);

        var externalUserId = exchange.getRequest().getHeaders().getFirst(EXTERNAL_USER_ID_HEADER);
        var authorizationHeader = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION);
        var sourceHeader = exchange.getRequest().getHeaders().getFirst(CustomHeaders.SOURCE);

        if (externalUserId != null && !externalUserId.isEmpty()) {
            LoggingUtils.logExternalUserStart(log, externalUserId, exchange);
            return authenticateExternalUser(exchange, chain, externalUserId, startTime);
        }

        if (authorizationHeader == null) {
            LoggingUtils.logJwtMissing(log, exchange);
            return onError(exchange, HttpStatus.UNAUTHORIZED, "Missing authorization header");
        }

        LoggingUtils.logJwtValidationStart(log, exchange);
        LoggingUtils.logWebClientRequest(log, "GET", validatorUrl, exchange);

        long jwtStartTime = System.currentTimeMillis();
        return webClient.build().get()
                .uri(validatorUrl)
                .header(AUTHORIZATION, authorizationHeader)
                .header(CustomHeaders.SOURCE, sourceHeader)
                .retrieve()
                .bodyToMono(TokenDetail.class)
                .flatMap(tokenDetail -> {
                    LoggingUtils.logJwtValidationSuccess(log, tokenDetail.claims().preferredUsername(), exchange, jwtStartTime);
                    LoggingUtils.logHeaderOperation(log, "added", "user-headers", exchange);

                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, tokenDetail.claims().preferredUsername())
                            .header(CustomHeaders.NAME, tokenDetail.claims().name())
                            .header(CustomHeaders.EMAIL, tokenDetail.claims().email())
                            .build();

                    LoggingUtils.logFilterExit(log, FILTER_NAME, exchange, startTime);
                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    LoggingUtils.logJwtValidationFailed(log, "WebClient error: " + e.getStatusCode(), exchange);
                    LoggingUtils.logWebClientError(log, e.getMessage(), validatorUrl, exchange, e);
                    return onError(exchange, HttpStatus.UNAUTHORIZED, "Token authentication failed: " + e.getMessage());
                })
                .onErrorResume(e -> {
                    LoggingUtils.logAuthenticationError(log, "Unexpected JWT validation error", exchange, e);
                    return onError(exchange, HttpStatus.UNAUTHORIZED, "An unexpected error occurred during authentication");
                });
    }

    private Mono<Void> authenticateExternalUser(ServerWebExchange exchange, GatewayFilterChain chain,
                                               String externalUserId, long filterStartTime) {
        String endpoint = externalUserEndpoint.replace("{id}", externalUserId);
        LoggingUtils.logWebClientRequest(log, "GET", endpoint, exchange);

        long externalUserStartTime = System.currentTimeMillis();
        return coreWebClient.get()
                .uri(uriBuilder -> uriBuilder.path(endpoint).build())
                .retrieve()
                .bodyToMono(UsuarioExternoDataStructure.class)
                .flatMap(userData -> {
                    LoggingUtils.logWebClientResponse(log, 200, exchange, externalUserStartTime);
                    log.debug("Datos usuario externo obtenidos: ID={}, Estado={}, Nombre={} - Request: {}",
                            userData.identificationNumber(), userData.status(),
                            RequestContextUtils.sanitizeToken(userData.name()),
                            RequestContextUtils.getRequestId(exchange));

                    if (!UsuarioExternoDataStructure.EstadoUsuarioExterno.ACTIVO.name().equals(userData.status())) {
                        LoggingUtils.logExternalUserInactive(log, externalUserId, userData.status(), exchange);
                        return onError(exchange, HttpStatus.UNAUTHORIZED, "External user is not active");
                    }

                    LoggingUtils.logExternalUserSuccess(log, userData.identificationNumber(), userData.status(),
                                                      exchange, externalUserStartTime);
                    LoggingUtils.logHeaderOperation(log, "added", "external-user-headers", exchange);

                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, userData.identificationNumber())
                            .header(CustomHeaders.NAME, userData.name())
                            .build();

                    LoggingUtils.logFilterExit(log, FILTER_NAME, exchange, filterStartTime);
                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    LoggingUtils.logWebClientError(log, e.getMessage(), endpoint, exchange, e);

                    if (e.getStatusCode().value() == 404) {
                        LoggingUtils.logExternalUserNotFound(log, externalUserId, exchange);
                        return onError(exchange, HttpStatus.UNAUTHORIZED, "External user not found");
                    }

                    HttpStatus status = HttpStatus.valueOf(e.getStatusCode().value());
                    String message = "External user authentication failed: " + e.getMessage();
                    return onError(exchange, status, message);
                })
                .onErrorResume(e -> {
                    LoggingUtils.logAuthenticationError(log, "Unexpected external user authentication error", exchange, e);
                    return onError(exchange, HttpStatus.INTERNAL_SERVER_ERROR,
                                 "An unexpected error occurred during external user authentication");
                });
    }
}
