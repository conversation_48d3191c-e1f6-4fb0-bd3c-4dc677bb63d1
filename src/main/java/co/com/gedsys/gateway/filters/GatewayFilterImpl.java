package co.com.gedsys.gateway.filters;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gateway.dtos.TokenDetail;
import co.com.gedsys.gateway.utils.LoggingUtils;
import co.com.gedsys.gateway.utils.RequestContextUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Component
public class GatewayFilterImpl implements GatewayFilter {
    private static final Logger log = LoggerFactory.getLogger(GatewayFilterImpl.class);
    private final WebClient.Builder webClient;
    private final String validatorUrl;
    private final ObjectMapper objectMapper;

    private static final String FILTER_NAME = "GatewayFilter";

    public GatewayFilterImpl(WebClient.Builder webClient,
                             @Value("${app.authentication-filter.token-validation-url}") String validatorUrl,
                             ObjectMapper objectMapper) {
        this.webClient = webClient;
        this.validatorUrl = validatorUrl;
        this.objectMapper = objectMapper;
    }

    public Mono<Void> onError(ServerWebExchange exchange, HttpStatus status, String message) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String clientIp = RequestContextUtils.getClientIp(exchange);
        String path = RequestContextUtils.getPath(exchange);

        log.error("Error en filtro JWT - Status: {} - Message: {} - Request: {} - IP: {} - Path: {}",
                status.value(), message, requestId, clientIp, path);

        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        errorResponse.put("path", path);
        errorResponse.put("requestId", requestId);

        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            return response.writeWith(Mono.just(response.bufferFactory().wrap(jsonResponse.getBytes())));
        } catch (JsonProcessingException e) {
            log.error("Error serializando respuesta de error - Request: {} - Error: {}", requestId, e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        long startTime = System.currentTimeMillis();
        LoggingUtils.logFilterEntry(log, FILTER_NAME, exchange);

        var authorizationHeader = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION);
        var sourceHeader = exchange.getRequest().getHeaders().getFirst(CustomHeaders.SOURCE);

        if (authorizationHeader == null) {
            LoggingUtils.logJwtMissing(log, exchange);
            return onError(exchange, HttpStatus.BAD_REQUEST, "Missing authorization header");
        }

        LoggingUtils.logJwtValidationStart(log, exchange);
        LoggingUtils.logWebClientRequest(log, "GET", validatorUrl, exchange);

        long jwtStartTime = System.currentTimeMillis();
        return webClient.build().get()
                .uri(validatorUrl)
                .header(AUTHORIZATION, authorizationHeader)
                .header(CustomHeaders.SOURCE, sourceHeader)
                .retrieve()
                .bodyToMono(TokenDetail.class)
                .flatMap(tokenDetail -> {
                    LoggingUtils.logJwtValidationSuccess(log, tokenDetail.claims().preferredUsername(), exchange, jwtStartTime);
                    LoggingUtils.logHeaderOperation(log, "added", "jwt-user-headers", exchange);

                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, tokenDetail.claims().preferredUsername())
                            .header(CustomHeaders.NAME, tokenDetail.claims().name())
                            .header(CustomHeaders.EMAIL, tokenDetail.claims().email())
                            .build();

                    LoggingUtils.logFilterExit(log, FILTER_NAME, exchange, startTime);
                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    LoggingUtils.logJwtValidationFailed(log, "WebClient error: " + e.getStatusCode(), exchange);
                    LoggingUtils.logWebClientError(log, e.getMessage(), validatorUrl, exchange, e);
                    HttpStatus status = HttpStatus.valueOf(e.getStatusCode().value());
                    return onError(exchange, status, "JWT validation failed: " + e.getMessage());
                })
                .onErrorResume(e -> {
                    LoggingUtils.logAuthenticationError(log, "Unexpected JWT validation error", exchange, e);
                    return onError(exchange, HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred");
                });
    }
}