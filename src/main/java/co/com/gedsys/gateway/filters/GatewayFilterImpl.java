package co.com.gedsys.gateway.filters;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gateway.dtos.TokenDetail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Component
public class GatewayFilterImpl implements GatewayFilter {
    Logger log = Logger.getLogger(GatewayFilterImpl.class.getName());
    private final WebClient.Builder webClient;
    private final String validatorUrl;
    private final ObjectMapper objectMapper;

    public GatewayFilterImpl(WebClient.Builder webClient,
                             @Value("${app.authentication-filter.token-validation-url}") String validatorUrl,
                             ObjectMapper objectMapper) {
        this.webClient = webClient;
        this.validatorUrl = validatorUrl;
        this.objectMapper = objectMapper;
    }

    public Mono<Void> onError(ServerWebExchange exchange, HttpStatus status, String message) {
        log.severe(message);
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        errorResponse.put("path", exchange.getRequest().getPath().value());

        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            return response.writeWith(Mono.just(response.bufferFactory().wrap(jsonResponse.getBytes())));
        } catch (JsonProcessingException e) {
            return Mono.error(e);
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        var authorizationHeader = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION);
        var sourceHeader = exchange.getRequest().getHeaders().getFirst(CustomHeaders.SOURCE);

        if (authorizationHeader == null) {
            return onError(exchange, HttpStatus.BAD_REQUEST, "Missing authorization header");
        }

        log.info("Validating JWT token standard");

        return webClient.build().get()
                .uri(validatorUrl)
                .header(AUTHORIZATION, authorizationHeader)
                .header(CustomHeaders.SOURCE, sourceHeader)
                .retrieve()
                .bodyToMono(TokenDetail.class)
                .flatMap(tokenDetail -> {
                    log.info("User authenticated: " + tokenDetail);
                    exchange.getRequest().mutate()
                            .header(CustomHeaders.GROUPS)
                            .header(CustomHeaders.USERNAME, tokenDetail.claims().preferredUsername())
                            .header(CustomHeaders.NAME, tokenDetail.claims().name())
                            .header(CustomHeaders.EMAIL, tokenDetail.claims().email())
                            .build();
                    return chain.filter(exchange);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    HttpStatus status = HttpStatus.valueOf(e.getStatusCode().value());
                    String message = e.getMessage();
                    return onError(exchange, status, message);
                })
                .onErrorResume(e -> onError(exchange, HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred"));
    }
}