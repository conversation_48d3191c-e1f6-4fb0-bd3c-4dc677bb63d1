package co.com.gedsys.gateway.filters;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.springframework.http.HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Component
public class SemanticSearchGatewayFilterImpl implements GatewayFilter {
    Logger log = Logger.getLogger(SemanticSearchGatewayFilterImpl.class.getName());
    private final String tenantToken;

    public SemanticSearchGatewayFilterImpl(@Value("${app.tenant-token}") String tenantToken) {
        this.tenantToken = tenantToken;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.info("Replacing authorization header with tenant token and rewriting path for semantic search service");

        String originalPath = exchange.getRequest().getPath().value();
        String newPath = originalPath.replaceFirst("^/semantic-search", "/webhook/documents/semantic-search");

        URI newUri = UriComponentsBuilder.fromUri(exchange.getRequest().getURI())
                .replacePath(newPath)
                .build()
                .toUri();

        var modifiedRequest = exchange.getRequest().mutate()
                .uri(newUri)
                .header(AUTHORIZATION, "Bearer " + tenantToken)
                .build();

        return chain.filter(exchange.mutate().request(modifiedRequest).build())
                .doFinally(signalType -> handleResponseHeaders(exchange));
    }

    private void handleResponseHeaders(ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getResponse().getHeaders();
        List<String> originHeaders = headers.get(ACCESS_CONTROL_ALLOW_ORIGIN);

        if (originHeaders != null && originHeaders.size() > 1) {
            String firstOrigin = originHeaders.getFirst();

            if (log.isLoggable(Level.WARNING)) {
                log.log(Level.WARNING, "Múltiples headers {0} detectados: {1}",
                    new Object[]{ACCESS_CONTROL_ALLOW_ORIGIN, originHeaders});
            }

            headers.set(ACCESS_CONTROL_ALLOW_ORIGIN, firstOrigin);

            if (log.isLoggable(Level.FINE)) {
                log.log(Level.FINE, "Headers {0} normalizados a: {1}",
                    new Object[]{ACCESS_CONTROL_ALLOW_ORIGIN, firstOrigin});
            }
        }
    }
}