package co.com.gedsys.gateway.utils;

public final class LoggingConstants {
    
    private LoggingConstants() {}
    
    public static final String REQUEST_ID_HEADER = "X-Request-ID";
    public static final String USER_AGENT_HEADER = "User-Agent";
    public static final String FORWARDED_FOR_HEADER = "X-Forwarded-For";
    public static final String REAL_IP_HEADER = "X-Real-IP";
    
    public static final String AUTH_START = "Iniciando autenticación para request: {}";
    public static final String AUTH_SUCCESS = "Autenticación exitosa para usuario: {} en {}ms";
    public static final String AUTH_FAILED = "Autenticación fallida: {} - Request: {} - IP: {}";
    public static final String AUTH_ERROR = "Error durante autenticación: {} - Request: {} - Causa: {}";
    
    public static final String EXTERNAL_USER_START = "Iniciando autenticación usuario externo ID: {} - Request: {}";
    public static final String EXTERNAL_USER_SUCCESS = "Usuario externo autenticado: {} - Estado: {} en {}ms";
    public static final String EXTERNAL_USER_NOT_FOUND = "Usuario externo no encontrado: {} - Request: {}";
    public static final String EXTERNAL_USER_INACTIVE = "Usuario externo inactivo: {} - Estado: {} - Request: {}";
    public static final String EXTERNAL_USER_ERROR = "Error autenticación usuario externo: {} - Request: {} - Causa: {}";
    
    public static final String JWT_VALIDATION_START = "Iniciando validación JWT - Request: {}";
    public static final String JWT_VALIDATION_SUCCESS = "JWT válido para usuario: {} en {}ms";
    public static final String JWT_VALIDATION_FAILED = "JWT inválido - Request: {} - Razón: {}";
    public static final String JWT_MISSING = "Header Authorization faltante - Request: {} - IP: {}";
    
    public static final String WEBCLIENT_REQUEST = "WebClient request: {} {} - Request: {}";
    public static final String WEBCLIENT_RESPONSE = "WebClient response: {} en {}ms - Request: {}";
    public static final String WEBCLIENT_ERROR = "WebClient error: {} - Request: {} - Endpoint: {}";
    public static final String WEBCLIENT_TIMEOUT = "WebClient timeout: {} - Request: {} - Endpoint: {}";
    
    public static final String FILTER_ENTRY = "Filtro {} iniciado - Request: {} - Path: {}";
    public static final String FILTER_EXIT = "Filtro {} completado en {}ms - Request: {}";
    public static final String FILTER_ERROR = "Error en filtro {}: {} - Request: {} - Path: {}";
    
    public static final String PATH_REWRITE = "Path reescrito: {} -> {} - Request: {}";
    public static final String HEADER_ADDED = "Header agregado: {} - Request: {}";
    public static final String HEADER_REPLACED = "Header reemplazado: {} - Request: {}";
    
    public static final String CORS_MULTIPLE_ORIGINS = "Múltiples headers CORS detectados: {} - Request: {}";
    public static final String CORS_NORMALIZED = "Headers CORS normalizados a: {} - Request: {}";
    
    public static final String CONFIG_LOADED = "Configuración cargada: {} = {}";
    public static final String CONFIG_ERROR = "Error en configuración: {} - Valor: {}";
    
    public static final String SENSITIVE_DATA_MASK = "***MASKED***";
}
