package co.com.gedsys.gateway.utils;

import org.slf4j.Logger;
import org.slf4j.MDC;
import org.springframework.web.server.ServerWebExchange;

import java.time.Instant;
import java.util.function.Supplier;

public final class LoggingUtils {
    
    private LoggingUtils() {}
    
    public static void logFilterEntry(Logger logger, String filterName, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String path = RequestContextUtils.getPath(exchange);
        
        MDC.put("requestId", requestId);
        MDC.put("clientIp", RequestContextUtils.getClientIp(exchange));
        MDC.put("userAgent", RequestContextUtils.getUserAgent(exchange));
        
        logger.info(LoggingConstants.FILTER_ENTRY, filterName, requestId, path);
    }
    
    public static void logFilterExit(Logger logger, String filterName, ServerWebExchange exchange, long startTime) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        long duration = System.currentTimeMillis() - startTime;
        
        logger.info(LoggingConstants.FILTER_EXIT, filterName, duration, requestId);
        
        MDC.clear();
    }
    
    public static void logFilterError(Logger logger, String filterName, ServerWebExchange exchange, 
                                    String error, Throwable throwable) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String path = RequestContextUtils.getPath(exchange);
        
        if (throwable != null) {
            logger.error(LoggingConstants.FILTER_ERROR, filterName, error, requestId, path, throwable);
        } else {
            logger.error(LoggingConstants.FILTER_ERROR, filterName, error, requestId, path);
        }
    }
    
    public static void logAuthenticationStart(Logger logger, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.info(LoggingConstants.AUTH_START, requestId);
    }
    
    public static void logAuthenticationSuccess(Logger logger, String username, ServerWebExchange exchange, long startTime) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        long duration = System.currentTimeMillis() - startTime;
        logger.info(LoggingConstants.AUTH_SUCCESS, username, duration);
    }
    
    public static void logAuthenticationFailed(Logger logger, String reason, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String clientIp = RequestContextUtils.getClientIp(exchange);
        logger.warn(LoggingConstants.AUTH_FAILED, reason, requestId, clientIp);
    }
    
    public static void logAuthenticationError(Logger logger, String error, ServerWebExchange exchange, Throwable cause) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        if (cause != null) {
            logger.error(LoggingConstants.AUTH_ERROR, error, requestId, cause.getMessage(), cause);
        } else {
            logger.error(LoggingConstants.AUTH_ERROR, error, requestId, "unknown");
        }
    }
    
    public static void logExternalUserStart(Logger logger, String userId, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.info(LoggingConstants.EXTERNAL_USER_START, userId, requestId);
    }
    
    public static void logExternalUserSuccess(Logger logger, String username, String status, 
                                            ServerWebExchange exchange, long startTime) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        long duration = System.currentTimeMillis() - startTime;
        logger.info(LoggingConstants.EXTERNAL_USER_SUCCESS, username, status, duration);
    }
    
    public static void logExternalUserNotFound(Logger logger, String userId, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.warn(LoggingConstants.EXTERNAL_USER_NOT_FOUND, userId, requestId);
    }
    
    public static void logExternalUserInactive(Logger logger, String userId, String status, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.warn(LoggingConstants.EXTERNAL_USER_INACTIVE, userId, status, requestId);
    }
    
    public static void logJwtValidationStart(Logger logger, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.info(LoggingConstants.JWT_VALIDATION_START, requestId);
    }
    
    public static void logJwtValidationSuccess(Logger logger, String username, ServerWebExchange exchange, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        logger.info(LoggingConstants.JWT_VALIDATION_SUCCESS, username, duration);
    }
    
    public static void logJwtValidationFailed(Logger logger, String reason, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.warn(LoggingConstants.JWT_VALIDATION_FAILED, requestId, reason);
    }
    
    public static void logJwtMissing(Logger logger, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        String clientIp = RequestContextUtils.getClientIp(exchange);
        logger.warn(LoggingConstants.JWT_MISSING, requestId, clientIp);
    }
    
    public static void logWebClientRequest(Logger logger, String method, String url, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.debug(LoggingConstants.WEBCLIENT_REQUEST, method, url, requestId);
    }
    
    public static void logWebClientResponse(Logger logger, int statusCode, ServerWebExchange exchange, long startTime) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        long duration = System.currentTimeMillis() - startTime;
        logger.debug(LoggingConstants.WEBCLIENT_RESPONSE, statusCode, duration, requestId);
    }
    
    public static void logWebClientError(Logger logger, String error, String endpoint, 
                                       ServerWebExchange exchange, Throwable cause) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        if (cause != null) {
            logger.error(LoggingConstants.WEBCLIENT_ERROR, error, requestId, endpoint, cause);
        } else {
            logger.error(LoggingConstants.WEBCLIENT_ERROR, error, requestId, endpoint);
        }
    }
    
    public static void logPathRewrite(Logger logger, String originalPath, String newPath, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.info(LoggingConstants.PATH_REWRITE, originalPath, newPath, requestId);
    }
    
    public static void logHeaderOperation(Logger logger, String operation, String headerName, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        if ("added".equals(operation)) {
            logger.debug(LoggingConstants.HEADER_ADDED, headerName, requestId);
        } else if ("replaced".equals(operation)) {
            logger.debug(LoggingConstants.HEADER_REPLACED, headerName, requestId);
        }
    }
    
    public static void logCorsMultipleOrigins(Logger logger, String origins, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.warn(LoggingConstants.CORS_MULTIPLE_ORIGINS, origins, requestId);
    }
    
    public static void logCorsNormalized(Logger logger, String finalOrigin, ServerWebExchange exchange) {
        String requestId = RequestContextUtils.getRequestId(exchange);
        logger.debug(LoggingConstants.CORS_NORMALIZED, finalOrigin, requestId);
    }
    
    public static <T> T measureTime(Logger logger, String operation, ServerWebExchange exchange, 
                                   Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        try {
            T result = supplier.get();
            long duration = System.currentTimeMillis() - startTime;
            logger.debug("{} completado en {}ms - Request: {}", 
                    operation, duration, RequestContextUtils.getRequestId(exchange));
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("{} falló después de {}ms - Request: {} - Error: {}", 
                    operation, duration, RequestContextUtils.getRequestId(exchange), e.getMessage(), e);
            throw e;
        }
    }
}
