package co.com.gedsys.gateway.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.util.UUID;

public final class RequestContextUtils {
    
    private RequestContextUtils() {}
    
    public static String getRequestId(ServerWebExchange exchange) {
        String requestId = exchange.getRequest().getHeaders().getFirst(LoggingConstants.REQUEST_ID_HEADER);
        if (requestId == null || requestId.isEmpty()) {
            requestId = UUID.randomUUID().toString().substring(0, 8);
            exchange.getRequest().mutate()
                    .header(LoggingConstants.REQUEST_ID_HEADER, requestId)
                    .build();
        }
        return requestId;
    }
    
    public static String getClientIp(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        
        String xForwardedFor = request.getHeaders().getFirst(LoggingConstants.FORWARDED_FOR_HEADER);
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst(LoggingConstants.REAL_IP_HEADER);
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        if (request.getRemoteAddress() != null) {
            return request.getRemoteAddress().getAddress().getHostAddress();
        }
        
        return "unknown";
    }
    
    public static String getUserAgent(ServerWebExchange exchange) {
        String userAgent = exchange.getRequest().getHeaders().getFirst(LoggingConstants.USER_AGENT_HEADER);
        return userAgent != null ? userAgent : "unknown";
    }
    
    public static String getPath(ServerWebExchange exchange) {
        return exchange.getRequest().getPath().value();
    }
    
    public static String getMethod(ServerWebExchange exchange) {
        return exchange.getRequest().getMethod().name();
    }
    
    public static String sanitizeAuthHeader(String authHeader) {
        if (authHeader == null || authHeader.isEmpty()) {
            return "null";
        }
        
        if (authHeader.toLowerCase().startsWith("bearer ")) {
            return "Bearer " + LoggingConstants.SENSITIVE_DATA_MASK;
        }
        
        return LoggingConstants.SENSITIVE_DATA_MASK;
    }
    
    public static String sanitizeToken(String token) {
        if (token == null || token.isEmpty()) {
            return "null";
        }
        
        if (token.length() <= 8) {
            return LoggingConstants.SENSITIVE_DATA_MASK;
        }
        
        return token.substring(0, 4) + LoggingConstants.SENSITIVE_DATA_MASK + token.substring(token.length() - 4);
    }
    
    public static String getRequestSummary(ServerWebExchange exchange) {
        return String.format("%s %s [%s]", 
                getMethod(exchange), 
                getPath(exchange), 
                getRequestId(exchange));
    }
}
