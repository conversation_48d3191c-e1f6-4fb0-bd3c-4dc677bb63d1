{"properties": [{"name": "cors.allowed-origins", "type": "java.lang.String", "description": "Dominios permitidos para hacer solicitudes CORS. Separados por comas. Ejemplo: http://localhost:8080, https://example.com.", "default": "http://localhost:4200"}, {"name": "cors.allowed-methods", "type": "java.lang.String", "description": "Metodos permitidos para las requests CORS. Separados por comas. Ejemplo: GET, POST, PUT, DELETE.", "default": "*"}, {"name": "cors.allowed-headers", "type": "java.lang.String", "description": "Headers permitidos para hacer solicitudes CORS. Separados por comas. Ejemplo: X-Custom-Header, Content-Type.", "default": "Origin, Content-Type, Accept, Authorization"}, {"name": "cors.mapping", "type": "java.lang.String", "description": "Patrón de rutas que interceptará el filtro CORS.", "default": "/**"}, {"name": "app.authentication-filter.token-validation-url", "type": "java.lang.String", "description": "Endpoint de validación de tokens.", "default": "lb://authenticator/auth/api/v1/authentication/valid"}, {"name": "app.hasura.admin-secret", "type": "java.lang.String", "description": "A description for 'app.hasura.admin-secret'"}, {"name": "app.hasura.uri", "type": "java.lang.String", "description": "A description for 'app.hasura.uri'"}, {"name": "app.webclient.gedsys2-base.uri", "type": "java.lang.String", "description": "A description for 'app.webclient.gedsys2-base.uri'"}, {"name": "app.hasura.graphql-endpoint", "type": "java.lang.String", "description": "A description for 'app.hasura.graphql-endpoint'"}, {"name": "app.tenant-token", "type": "java.lang.String", "description": "Token de tenant para la aplicación y el webhook de semantic search"}]}