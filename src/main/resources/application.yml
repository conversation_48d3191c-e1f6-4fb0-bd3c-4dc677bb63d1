app:
  authentication-filter:
    token-validation-url: http://kcadapter:8081/auth/api/v1/authentication/valid
  webclient:
    gedsys2-base:
      uri: ${GEDSYS2-BASE_URI:http://gedsys2-base:8080/core}
      endpoints:
        - id: external-user-by-id
          path: /api/v1/planeacion/usuarios-externos/{id}
  hasura:
    admin-secret: ${HASURA_ADMIN_SECRET:my-secret}
    uri: ${HASURA_URI:http://hasura:8080}
    graphql-endpoint: ${HASURA_GRAPHQL_ENDPOINT:v1/graphql}
    
  tenant-token: ${TENANT_TOKEN:tenant-token}
  files-connector:
    tenant: ${TENANT_ID:tenant}
    api-key: ${API_KEY_FILES_CONNECTOR:api-key}

management:
  endpoints:
    web:
      exposure:
        include: '*'

server:
  port: 9000

logging:
  level:
    co.com.gedsys.gateway.filters: DEBUG
    co.com.gedsys.gateway.config: DEBUG
    co.com.gedsys.gateway: DEBUG

spring:
  application:
    name: gateway
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: ${ALLOWED_ORIGINS:http://localhost:4200}
            allowed-methods:
              - POST
              - GET
              - PUT
              - DELETE
              - PATCH
              - OPTIONS
            allowed-headers: "*"
      routes:
        - id: external-user-documents-route
          uri: http://gedsys2-base:8080
          predicates:
            - Path=/core/api/v1/gestion-tramite/busqueda/documentos/{documentId}
          filters:
            - ExternalUserAuthenticationFilter

        - id: base-route
          uri: http://gedsys2-base:8080
          predicates:
            - Path=/core/**
          filters:
            - AuthenticationFilter

        - id: auth-route
          uri: http://kcadapter:8081
          predicates:
            - Path=/auth/api/v1/**

        - id: camunda-route
          uri: http://gedsys2-bpm-engine:8080
          predicates:
            - Path=/engine-rest/**

        - id: engine-custom-route
          uri: http://gedsys2-bpm-engine:8080
          predicates:
            - Path=/engine/api/**
          filters:
            - AuthenticationFilter
   
        - id: aware-main-route
          uri: http://gedsys2-aware:8080
          predicates:
            - Path=/aware/api/**
          filters:
            - AuthenticationFilter

        - id: aware-socket-route
          uri: http://gedsys2-aware:8080
          predicates:
            - Path=/aware/ws

        - id: artificial-intelligence-component
          uri: http://gedsys2-ai:8080
          predicates:
            - Path=/ai/**
          filters:
            - AuthenticationFilter

        - id: gedsys2-config
          uri: http://gedsys2-config:8080
          predicates:
            - Path=/config/**
          filters:
            - AuthenticationFilter

        - id: graphql-service
          uri: ${app.hasura.uri}
          predicates:
            - Path=/hasura/v1/graphql
          filters:
            - AuthenticationFilter
            - HasuraAdminSecret
            - RewritePath=/hasura/(?<segment>.*), /${segment}
            
        - id: files-connector
          uri: https://files-connector.gedsys.dev
          predicates:
            - Path=/files/**
          filters:
            - AuthenticationFilter
            - AddRequestHeader=x-api-Key, ${app.files-connector.api-key}
            - AddRequestHeader=x-tenant, ${app.files-connector.tenant}
            - RewritePath=/files/(?<segment>.*), /${segment}

        - id: semantic-search
          uri: https://automation.gedsys.dev
          predicates:
            - Path=/semantic-search/**
          filters:
            - AuthenticationFilter
            - SemanticSearchAuthenticationFilter
        