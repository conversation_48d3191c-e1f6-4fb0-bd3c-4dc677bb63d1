package co.com.gedsys.gateway.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class HasuraAdminSecretFilterTest {

    private HasuraAdminSecretFilter filter;

    @Mock
    private GatewayFilterChain chain;

    @Captor
    private ArgumentCaptor<ServerWebExchange> exchangeCaptor;

    private final String adminSecret = "test-admin-secret";
    private static final String ACCESS_CONTROL_ALLOW_ORIGIN = "Access-Control-Allow-Origin";

    @BeforeEach
    void setUp() {
        filter = new HasuraAdminSecretFilter(adminSecret);

        // Configurar el comportamiento del chain para capturar el exchange y simular la respuesta
        doAnswer(invocation -> {
            ServerWebExchange exchange = invocation.getArgument(0);
            // Simular que el chain.filter() completa sin errores
            return Mono.empty();
        }).when(chain).filter(any(ServerWebExchange.class));
    }

    @Test
    void shouldAddHasuraAdminSecretHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/hasura/v1/graphql")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(chain).filter(exchangeCaptor.capture());
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();

        // Verificar que el header x-hasura-admin-secret fue agregado con el valor correcto
        assertTrue(capturedExchange.getRequest().getHeaders().containsKey("x-hasura-admin-secret"));
        assertEquals(adminSecret, capturedExchange.getRequest().getHeaders().getFirst("x-hasura-admin-secret"));
    }

    @Test
    void shouldPreserveExistingHeaders() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/hasura/v1/graphql")
                .header("Authorization", "Bearer token123")
                .header("Content-Type", "application/json")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(chain).filter(exchangeCaptor.capture());
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();

        // Verificar que el header x-hasura-admin-secret fue agregado
        assertTrue(capturedExchange.getRequest().getHeaders().containsKey("x-hasura-admin-secret"));
        assertEquals(adminSecret, capturedExchange.getRequest().getHeaders().getFirst("x-hasura-admin-secret"));

        // Verificar que los headers existentes se mantuvieron
        assertEquals("Bearer token123", capturedExchange.getRequest().getHeaders().getFirst("Authorization"));
        assertEquals("application/json", capturedExchange.getRequest().getHeaders().getFirst("Content-Type"));
    }

    @Test
    void shouldRemoveDuplicateAccessControlAllowOriginHeaders() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/hasura/v1/graphql").build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Simular cabeceras duplicadas en la respuesta
        exchange.getResponse().getHeaders().add(ACCESS_CONTROL_ALLOW_ORIGIN, "http://localhost:4200");
        exchange.getResponse().getHeaders().add(ACCESS_CONTROL_ALLOW_ORIGIN, "http://localhost:4200");

        // When
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Verificar que solo queda una cabecera Access-Control-Allow-Origin
        HttpHeaders responseHeaders = exchange.getResponse().getHeaders();
        assertEquals(1, responseHeaders.get(ACCESS_CONTROL_ALLOW_ORIGIN).size());
        assertEquals("http://localhost:4200", responseHeaders.getFirst(ACCESS_CONTROL_ALLOW_ORIGIN));
    }

    @Test
    void shouldNotModifySingleAccessControlAllowOriginHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/hasura/v1/graphql").build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Simular una única cabecera en la respuesta
        exchange.getResponse().getHeaders().add(ACCESS_CONTROL_ALLOW_ORIGIN, "http://localhost:4200");

        // When
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Verificar que la cabecera sigue intacta
        HttpHeaders responseHeaders = exchange.getResponse().getHeaders();
        assertEquals(1, responseHeaders.get(ACCESS_CONTROL_ALLOW_ORIGIN).size());
        assertEquals("http://localhost:4200", responseHeaders.getFirst(ACCESS_CONTROL_ALLOW_ORIGIN));
    }

    @Test
    void shouldHandleNoAccessControlAllowOriginHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/hasura/v1/graphql").build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // No agregamos ninguna cabecera Access-Control-Allow-Origin

        // When
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Verificar que no hay cabecera Access-Control-Allow-Origin
        HttpHeaders responseHeaders = exchange.getResponse().getHeaders();
        assertFalse(responseHeaders.containsKey(ACCESS_CONTROL_ALLOW_ORIGIN));
    }
}
