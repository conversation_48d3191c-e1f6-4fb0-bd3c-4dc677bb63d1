package co.com.gedsys.gateway.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;

import static org.junit.jupiter.api.Assertions.assertSame;

@ExtendWith(MockitoExtension.class)
class HasuraAdminSecretGatewayFilterFactoryTest {

    private HasuraAdminSecretGatewayFilterFactory factory;

    @Mock
    private HasuraAdminSecretFilter hasuraAdminSecretFilter;

    @BeforeEach
    void setUp() {
        factory = new HasuraAdminSecretGatewayFilterFactory(hasuraAdminSecretFilter);
    }

    @Test
    void shouldReturnHasuraAdminSecretFilter() {
        // Given
        HasuraAdminSecretGatewayFilterFactory.Config config = new HasuraAdminSecretGatewayFilterFactory.Config();

        // When
        GatewayFilter filter = factory.apply(config);

        // Then
        assertSame(hasuraAdminSecretFilter, filter);
    }
}
