package co.com.gedsys.gateway.config;

import co.com.gedsys.gateway.filters.SemanticSearchGatewayFilterImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class SemanticSearchAuthenticationFilterTest {

    @Mock
    private SemanticSearchGatewayFilterImpl semanticSearchGatewayFilter;

    private SemanticSearchAuthenticationFilter filter;

    @BeforeEach
    void setUp() {
        filter = new SemanticSearchAuthenticationFilter(semanticSearchGatewayFilter);
    }

    @Test
    void shouldReturnGatewayFilterFromApply() {
        // Given
        SemanticSearchAuthenticationFilter.Config config = new SemanticSearchAuthenticationFilter.Config();

        // When
        GatewayFilter result = filter.apply(config);

        // Then
        assertNotNull(result);
        assertEquals(semanticSearchGatewayFilter, result);
    }

    @Test
    void shouldCreateConfigObject() {
        // Given & When
        SemanticSearchAuthenticationFilter.Config config = new SemanticSearchAuthenticationFilter.Config();

        // Then
        assertNotNull(config);
    }
} 