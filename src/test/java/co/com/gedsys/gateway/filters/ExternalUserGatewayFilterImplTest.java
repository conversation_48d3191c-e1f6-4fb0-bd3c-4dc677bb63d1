package co.com.gedsys.gateway.filters;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gateway.datastructures.client.responses.usuario_externo.UsuarioExternoDataStructure;
import co.com.gedsys.gateway.dtos.TokenDetail;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@ExtendWith(MockitoExtension.class)
class ExternalUserGatewayFilterImplTest {

    private ExternalUserGatewayFilterImpl filter;

    @Mock
    private WebClient.Builder webClientBuilder;

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private GatewayFilterChain chain;

    @Mock
    private ObjectMapper objectMapper;

    private final String validatorUrl = "http://validator-url";
    private final String externalUserEndpoint = "/api/v1/planeacion/usuarios-externos/{id}";

    @BeforeEach
    void setUp() throws Exception {
        filter = new ExternalUserGatewayFilterImpl(
                webClientBuilder,
                validatorUrl,
                webClient,
                externalUserEndpoint,
                objectMapper
        );

        // Mock WebClient for token validation - using lenient() for all mocks to avoid UnnecessaryStubbingException
        lenient().when(webClientBuilder.build()).thenReturn(webClient);
        lenient().when(webClient.get()).thenReturn(requestHeadersUriSpec);

        // Mock URI methods with lenient mode to avoid strict stubbing issues
        lenient().when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        lenient().when(requestHeadersUriSpec.uri(any(java.util.function.Function.class))).thenReturn(requestHeadersSpec);

        // Mock header chain
        lenient().when(requestHeadersSpec.header(anyString(), anyString())).thenReturn(requestHeadersSpec);
        lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

        // Mock chain.filter to return empty Mono
        lenient().when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Mock objectMapper for error responses
        lenient().when(objectMapper.writeValueAsString(any())).thenReturn("{\"error\":\"test error\"}");
    }

    @Test
    void shouldAuthenticateWithActiveExternalUserId() {
        // Given
        String externalUserId = "123456789";
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .header("X-External-User-Id", externalUserId)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Mock external user data with ACTIVE status
        UsuarioExternoDataStructure userData = new UsuarioExternoDataStructure(
                Collections.emptyList(),
                "987654321", // identificationNumber puede ser diferente al externalUserId
                "CC",
                "Test User",
                "Notes",
                "Mr.",
                UsuarioExternoDataStructure.EstadoUsuarioExterno.ACTIVO.name(),
                "user-id-123"
        );

        // Mock WebClient response for external user
        when(responseSpec.bodyToMono(UsuarioExternoDataStructure.class)).thenReturn(Mono.just(userData));

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchange);

        // Verify that the request was mutated with the user details
        assertEquals("987654321", exchange.getRequest().getHeaders().getFirst(CustomHeaders.USERNAME));
        assertEquals("Test User", exchange.getRequest().getHeaders().getFirst(CustomHeaders.NAME));
        assertEquals(externalUserId, exchange.getRequest().getHeaders().getFirst("X-External-User-Id"));
    }

    @Test
    void shouldRejectInactiveExternalUser() {
        // Given
        String externalUserId = "123456789";
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .header("X-External-User-Id", externalUserId)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Mock external user data with INACTIVE status
        UsuarioExternoDataStructure userData = new UsuarioExternoDataStructure(
                Collections.emptyList(),
                externalUserId,
                "CC",
                "Test User",
                "Notes",
                "Mr.",
                UsuarioExternoDataStructure.EstadoUsuarioExterno.INACTIVO.name(),
                "user-id-123"
        );

        // Mock WebClient response for external user
        when(responseSpec.bodyToMono(UsuarioExternoDataStructure.class)).thenReturn(Mono.just(userData));

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain, never()).filter(exchange);
        // Verify that the response status is UNAUTHORIZED (401)
        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
    }

    @Test
    void shouldFallbackToTokenAuthenticationWhenNoExternalUserId() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .header(AUTHORIZATION, "Bearer token123")
                .header(CustomHeaders.SOURCE, "test-source")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Mock token details
        TokenDetail.Claims claims = new TokenDetail.Claims("user123", "User Name", "<EMAIL>");
        TokenDetail tokenDetail = new TokenDetail(claims);

        // Mock WebClient response for token validation
        when(responseSpec.bodyToMono(TokenDetail.class)).thenReturn(Mono.just(tokenDetail));

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchange);
        verify(webClientBuilder).build();

        // Verify that the request was mutated with the user details
        verify(requestHeadersSpec).header(eq(AUTHORIZATION), eq("Bearer token123"));
        verify(requestHeadersSpec).header(eq(CustomHeaders.SOURCE), eq("test-source"));
    }

    @Test
    void shouldReturnUnauthorizedWhenNoExternalUserIdAndNoAuthorizationHeader() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain, never()).filter(exchange);
        // Verify that the response status is UNAUTHORIZED (401) instead of BAD_REQUEST (400)
        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
    }

    @Test
    void shouldReturnUnauthorizedWhenExternalUserNotFound() {
        // Given
        String externalUserId = "123456789";
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .header("X-External-User-Id", externalUserId)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Mock external user authentication failure with 404 Not Found
        WebClientResponseException notFoundException = WebClientResponseException.create(
                404, "Not Found", null, null, null);
        when(responseSpec.bodyToMono(UsuarioExternoDataStructure.class))
            .thenReturn(Mono.error(notFoundException));

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain, never()).filter(exchange);
        // Verify that the response status is UNAUTHORIZED (401)
        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
    }

    @Test
    void shouldReturnOriginalStatusWhenExternalUserAuthenticationFailsWithNon404Error() {
        // Given
        String externalUserId = "123456789";
        MockServerHttpRequest request = MockServerHttpRequest.get("/some-path")
                .header("X-External-User-Id", externalUserId)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Mock external user authentication failure with 500 Internal Server Error
        WebClientResponseException serverErrorException = WebClientResponseException.create(
                500, "Internal Server Error", null, null, null);
        when(responseSpec.bodyToMono(UsuarioExternoDataStructure.class))
            .thenReturn(Mono.error(serverErrorException));

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain, never()).filter(exchange);
        // Verify that the response status is the original error (500)
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exchange.getResponse().getStatusCode());
    }
}

