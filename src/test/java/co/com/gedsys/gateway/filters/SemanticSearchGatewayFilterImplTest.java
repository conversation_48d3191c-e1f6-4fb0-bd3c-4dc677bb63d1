package co.com.gedsys.gateway.filters;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@ExtendWith(MockitoExtension.class)
class SemanticSearchGatewayFilterImplTest {

    private SemanticSearchGatewayFilterImpl filter;

    @Mock
    private GatewayFilterChain chain;

    private final String tenantToken = "test-tenant-token";

    @BeforeEach
    void setUp() {
        filter = new SemanticSearchGatewayFilterImpl(tenantToken);
        
        // Mock chain.filter to return empty Mono (lenient for tests that don't need it)
        lenient().when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
    }

    @Test
    void shouldReplaceAuthorizationHeaderWithTenantToken() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/test")
                .header(AUTHORIZATION, "Bearer original-jwt-token")
                .header("X-Custom-Header", "custom-value")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchangeCaptor.capture());
        
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        HttpHeaders headers = capturedExchange.getRequest().getHeaders();
        
        // Verify the authorization header was replaced with tenant token
        assertEquals("Bearer " + tenantToken, headers.getFirst(AUTHORIZATION));
        
        // Verify other headers remain unchanged
        assertEquals("custom-value", headers.getFirst("X-Custom-Header"));
    }

    @Test
    void shouldReplaceAuthorizationHeaderEvenWhenEmpty() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/test")
                .build(); // No authorization header
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchangeCaptor.capture());
        
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        HttpHeaders headers = capturedExchange.getRequest().getHeaders();
        
        // Verify the authorization header was added with tenant token
        assertEquals("Bearer " + tenantToken, headers.getFirst(AUTHORIZATION));
    }

    @Test
    void shouldReplaceAuthorizationHeaderWithDifferentFormat() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/test")
                .header(AUTHORIZATION, "Basic some-basic-auth")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchangeCaptor.capture());
        
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        HttpHeaders headers = capturedExchange.getRequest().getHeaders();
        
        // Verify the authorization header was replaced with tenant token
        assertEquals("Bearer " + tenantToken, headers.getFirst(AUTHORIZATION));
    }

    @Test
    void shouldPreserveAllOtherHeaders() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/test")
                .header(AUTHORIZATION, "Bearer original-token")
                .header("Content-Type", "application/json")
                .header("X-User-Id", "user123")
                .header("X-Source", "mobile-app")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchangeCaptor.capture());
        
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        HttpHeaders headers = capturedExchange.getRequest().getHeaders();
        
        // Verify authorization header was replaced
        assertEquals("Bearer " + tenantToken, headers.getFirst(AUTHORIZATION));
        
        // Verify all other headers are preserved
        assertEquals("application/json", headers.getFirst("Content-Type"));
        assertEquals("user123", headers.getFirst("X-User-Id"));
        assertEquals("mobile-app", headers.getFirst("X-Source"));
    }

    @Test
    void shouldPreserveRequestPath() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/documents/search")
                .header(AUTHORIZATION, "Bearer original-token")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(exchangeCaptor.capture());
        
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        
        // Verify path is preserved
        assertEquals("/semantic-search/documents/search", capturedExchange.getRequest().getPath().value());
        
        // Verify authorization header was replaced
        assertEquals("Bearer " + tenantToken, capturedExchange.getRequest().getHeaders().getFirst(AUTHORIZATION));
    }

    @Test
    void shouldCreateFilterInstance() {
        // Then
        assertNotNull(filter);
    }

    @Test
    void shouldCallChainWithModifiedExchange() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/semantic-search/test")
                .header(AUTHORIZATION, "Bearer jwt-token")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // When
        filter.filter(exchange, chain).block();

        // Then
        verify(chain).filter(any(ServerWebExchange.class));
    }
} 